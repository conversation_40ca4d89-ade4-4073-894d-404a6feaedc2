# File generated from our OpenAPI spec by <PERSON><PERSON>less. See CONTRIBUTING.md for details.

from typing import Union
from typing_extensions import Literal, Annotated, TypeAlias

from ..._utils import PropertyInfo
from ..._models import BaseModel
from .beta_text_block import <PERSON>Text<PERSON>lock
from .beta_thinking_block import <PERSON><PERSON>hinking<PERSON><PERSON>
from .beta_tool_use_block import <PERSON><PERSON>ool<PERSON>se<PERSON>lock
from .beta_mcp_tool_use_block import <PERSON><PERSON>P<PERSON>oolUseBlock
from .beta_mcp_tool_result_block import BetaMCPToolResultBlock
from .beta_server_tool_use_block import BetaServerToolUseBlock
from .beta_container_upload_block import <PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON>
from .beta_redacted_thinking_block import BetaRedactedThinking<PERSON>lock
from .beta_web_search_tool_result_block import BetaWebSearchToolResultBlock
from .beta_code_execution_tool_result_block import BetaCodeExecutionToolResultBlock

__all__ = ["BetaRawContentBlockStartEvent", "ContentBlock"]

ContentBlock: TypeAlias = Annotated[
    Union[
        BetaTextBlock,
        <PERSON>Thinking<PERSON>lock,
        BetaR<PERSON>ctedThinking<PERSON><PERSON>,
        <PERSON>ToolUse<PERSON>lock,
        BetaServerToolUseBlock,
        BetaWebSearchToolResultBlock,
        BetaCodeExecutionToolResultBlock,
        BetaMCPToolUseBlock,
        BetaMCPToolResultBlock,
        BetaContainerUploadBlock,
    ],
    PropertyInfo(discriminator="type"),
]


class BetaRawContentBlockStartEvent(BaseModel):
    content_block: ContentBlock
    """Response model for a file uploaded to the container."""

    index: int

    type: Literal["content_block_start"]
