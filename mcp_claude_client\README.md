# MCP Claude Client

A Python client for integrating MCP servers with Claude API.

This client allows you to connect to any MCP (Model Context Protocol) server and use <PERSON>'s language model capabilities with the available MCP tools.

## Features

- **MCP Integration**: Connect to any MCP server (file, HTTP, or command-based)
- **Claude API Integration**: Use <PERSON>'s language model capabilities
- **Tool Discovery**: Automatically discover available tools from the MCP server
- **Tool Execution**: Execute MCP tools based on <PERSON>'s responses
- **Conversation Management**: Handle multi-turn conversations with tool usage
- **Configuration Management**: Flexible configuration via files or environment variables
- **CLI Interface**: Simple command-line interface for interacting with the client

## Installation

### Prerequisites

- Python 3.10 or higher
- A Claude API key from Anthropic
- An MCP server to connect to (e.g., fastMCP server)

### Install from Source

```bash
# Clone the repository
git clone https://github.com/example/mcp-claude-client.git
cd mcp-claude-client

# Install the package
pip install -e .
```

### Install Dependencies Only

```bash
# Navigate to the directory
cd mcp_claude_client

# Install dependencies
pip install -r requirements.txt
```

## Configuration

The client can be configured in several ways:

1. **Environment Variables**: Set environment variables like `CLAUDE_API_KEY` and `MCP_SERVER_PATH`
2. **Configuration File**: Create a JSON configuration file
3. **Programmatic Configuration**: Pass configuration options directly to the client

### Environment Variables

Create a `.env` file based on the provided `.env.example`:

```bash
# Claude API settings
CLAUDE_API_KEY=your-claude-api-key-here
CLAUDE_MODEL=claude-3-5-sonnet-20241022
CLAUDE_MAX_TOKENS=4096

# MCP Server settings (choose one)
MCP_SERVER_PATH=../simple_mcp_server.py
# MCP_SERVER_URL=http://localhost:8000
# MCP_SERVER_COMMAND=python
# MCP_SERVER_ARGS=../simple_mcp_server.py

# Client settings
MCP_CONNECTION_TIMEOUT=30
MCP_REQUEST_TIMEOUT=60
MCP_MAX_RETRIES=3
LOG_LEVEL=INFO
```

### Configuration File

Create a JSON configuration file based on the provided `config_example.json`:

```json
{
  "claude_api_key": "your-claude-api-key-here",
  "claude_model": "claude-3-5-sonnet-20241022",
  "claude_max_tokens": 4096,
  "mcp_server_path": "../simple_mcp_server.py",
  "connection_timeout": 30,
  "request_timeout": 60,
  "max_retries": 3,
  "log_level": "INFO"
}
```

## Usage

### Command-Line Interface

The client provides a simple command-line interface:

```bash
# Start interactive chat
python -m mcp_claude_client.cli chat

# Send a single message
python -m mcp_claude_client.cli message "What time is it?"

# List available tools
python -m mcp_claude_client.cli tools

# Show current configuration
python -m mcp_claude_client.cli config

# Use custom config file
python -m mcp_claude_client.cli --config config.json chat
```

### Programmatic Usage

```python
import asyncio
from mcp_claude_client import MCPClaudeClient, ClientConfig

async def main():
    # Create configuration
    config = ClientConfig(
        claude_api_key="your-claude-api-key",
        mcp_server_path="../simple_mcp_server.py"
    )
    
    # Create client
    client = MCPClaudeClient(config)
    
    # Connect to MCP server
    await client.connect()
    
    try:
        # Send a message to Claude
        response = await client.send_message(
            "What time is it and what tools are available?"
        )
        
        print(response)
        
        # List available tools
        tools = client.get_available_tools()
        print(f"Available tools: {tools}")
        
    finally:
        # Disconnect from MCP server
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
```

## MCP Server Compatibility

This client is compatible with any MCP server, including:

- **FastMCP Servers**: Connect to any FastMCP server
- **HTTP MCP Servers**: Connect to MCP servers over HTTP
- **File-Based MCP Servers**: Connect to MCP servers via file path
- **Command-Based MCP Servers**: Start and connect to MCP servers via command

## Examples

### Connect to a File-Based MCP Server

```python
from mcp_claude_client import MCPClaudeClient, ClientConfig

config = ClientConfig(
    claude_api_key="your-claude-api-key",
    mcp_server_path="../simple_mcp_server.py"
)

client = MCPClaudeClient(config)
```

### Connect to an HTTP MCP Server

```python
from mcp_claude_client import MCPClaudeClient, ClientConfig

config = ClientConfig(
    claude_api_key="your-claude-api-key",
    mcp_server_url="http://localhost:8000"
)

client = MCPClaudeClient(config)
```

### Connect to a Command-Based MCP Server

```python
from mcp_claude_client import MCPClaudeClient, ClientConfig

config = ClientConfig(
    claude_api_key="your-claude-api-key",
    mcp_server_command="python",
    mcp_server_args=["../simple_mcp_server.py"]
)

client = MCPClaudeClient(config)
```

## License

MIT License
