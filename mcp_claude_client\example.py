#!/usr/bin/env python3
"""
Simple example of using the MCP Claude Client.

This example demonstrates how to:
1. Configure the client
2. Connect to an MCP server
3. Send messages to <PERSON> with tool access
4. Handle responses and tool executions
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import the client
sys.path.insert(0, str(Path(__file__).parent.parent))

from mcp_claude_client import MCPClaudeClient, ClientConfig


async def simple_example():
    """Simple example of using the MCP Claude Client."""
    print("🚀 MCP Claude Client Example")
    print("=" * 40)
    
    # Check for Claude API key
    claude_api_key = os.getenv("CLAUDE_API_KEY")
    if not claude_api_key:
        print("❌ Please set the CLAUDE_API_KEY environment variable")
        print("   You can get an API key from: https://console.anthropic.com/")
        return
    
    try:
        # Create configuration
        config = ClientConfig(
            claude_api_key=claude_api_key,
            mcp_server_path="../simple_mcp_server.py",  # Path to the simple MCP server
            log_level="INFO"
        )
        
        # Create and connect the client
        client = MCPClaudeClient(config)
        
        async with client:
            print("✅ Connected to MCP server and Claude API")
            
            # Show available tools
            tools = client.get_available_tools()
            print(f"\n📋 Available Tools ({len(tools)}):")
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
            
            # Example conversations
            examples = [
                "Hello! Can you introduce yourself?",
                "What time is it?",
                "Can you greet me formally?",
                "Calculate the sum of 15.5 and 24.3",
                "What tools do you have available?",
            ]
            
            print(f"\n🤖 Example Conversations:")
            print("-" * 40)
            
            for i, message in enumerate(examples, 1):
                print(f"\n{i}. 👤 User: {message}")
                print("🤖 Claude: ", end="", flush=True)
                
                try:
                    response = await client.send_message(message)
                    print(response)
                except Exception as e:
                    print(f"❌ Error: {e}")
                
                # Small delay between messages
                await asyncio.sleep(1)
            
            print(f"\n✅ Example completed successfully!")
            
    except Exception as e:
        print(f"❌ Error: {e}")


async def interactive_example():
    """Interactive example allowing user input."""
    print("🚀 MCP Claude Client - Interactive Example")
    print("=" * 50)
    
    # Check for Claude API key
    claude_api_key = os.getenv("CLAUDE_API_KEY")
    if not claude_api_key:
        print("❌ Please set the CLAUDE_API_KEY environment variable")
        return
    
    try:
        # Create configuration
        config = ClientConfig(
            claude_api_key=claude_api_key,
            mcp_server_path="../simple_mcp_server.py",
            log_level="INFO"
        )
        
        # Create and connect the client
        client = MCPClaudeClient(config)
        
        async with client:
            print("✅ Connected to MCP server and Claude API")
            
            # Show available tools
            tools = client.get_available_tools()
            print(f"\n📋 Available Tools: {', '.join(tool['name'] for tool in tools)}")
            
            print("\n💬 Interactive Chat (type 'quit' to exit)")
            print("-" * 50)
            
            while True:
                try:
                    user_input = input("\n👤 You: ").strip()
                    
                    if not user_input:
                        continue
                    
                    if user_input.lower() in ["quit", "exit", "bye"]:
                        break
                    
                    print("🤖 Claude: ", end="", flush=True)
                    response = await client.send_message(user_input)
                    print(response)
                    
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"\n❌ Error: {e}")
            
            print("\n👋 Goodbye!")
            
    except Exception as e:
        print(f"❌ Error: {e}")


async def main():
    """Main function to run examples."""
    print("Choose an example to run:")
    print("1. Simple example (predefined messages)")
    print("2. Interactive example (user input)")
    print("3. Both examples")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            await simple_example()
        elif choice == "2":
            await interactive_example()
        elif choice == "3":
            await simple_example()
            print("\n" + "=" * 60)
            await interactive_example()
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")


if __name__ == "__main__":
    print("🎯 MCP Claude Client Examples")
    print("Make sure you have:")
    print("  1. Set CLAUDE_API_KEY environment variable")
    print("  2. The simple MCP server at ../simple_mcp_server.py")
    print("  3. Installed required dependencies")
    print()
    
    asyncio.run(main())
