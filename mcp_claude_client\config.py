"""
Configuration management for the MCP Claude Client.
"""

import os
import json
from typing import Optional, Dict, Any
from dataclasses import dataclass, field
from pathlib import Path

from .exceptions import ConfigurationError


@dataclass
class ClientConfig:
    """Configuration for the MCP Claude Client."""
    
    # Claude API settings
    claude_api_key: Optional[str] = None
    claude_model: str = "claude-3-5-sonnet-20241022"
    claude_max_tokens: int = 4096
    
    # MCP Server settings
    mcp_server_path: Optional[str] = None
    mcp_server_url: Optional[str] = None
    mcp_server_command: Optional[str] = None
    mcp_server_args: list[str] = field(default_factory=list)
    mcp_server_env: Dict[str, str] = field(default_factory=dict)
    
    # Client settings
    connection_timeout: int = 30
    request_timeout: int = 60
    max_retries: int = 3
    log_level: str = "INFO"
    
    @classmethod
    def from_env(cls) -> "ClientConfig":
        """Create configuration from environment variables."""
        return cls(
            claude_api_key=os.getenv("CLAUDE_API_KEY"),
            claude_model=os.getenv("CLAUDE_MODEL", "claude-3-5-sonnet-20241022"),
            claude_max_tokens=int(os.getenv("CLAUDE_MAX_TOKENS", "4096")),
            mcp_server_path=os.getenv("MCP_SERVER_PATH"),
            mcp_server_url=os.getenv("MCP_SERVER_URL"),
            mcp_server_command=os.getenv("MCP_SERVER_COMMAND"),
            mcp_server_args=os.getenv("MCP_SERVER_ARGS", "").split() if os.getenv("MCP_SERVER_ARGS") else [],
            connection_timeout=int(os.getenv("MCP_CONNECTION_TIMEOUT", "30")),
            request_timeout=int(os.getenv("MCP_REQUEST_TIMEOUT", "60")),
            max_retries=int(os.getenv("MCP_MAX_RETRIES", "3")),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
        )
    
    @classmethod
    def from_file(cls, config_path: str) -> "ClientConfig":
        """Create configuration from a JSON file."""
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            
            return cls(**config_data)
        except FileNotFoundError:
            raise ConfigurationError(f"Configuration file not found: {config_path}")
        except json.JSONDecodeError as e:
            raise ConfigurationError(f"Invalid JSON in configuration file: {e}")
        except TypeError as e:
            raise ConfigurationError(f"Invalid configuration format: {e}")
    
    def validate(self) -> None:
        """Validate the configuration."""
        if not self.claude_api_key:
            raise ConfigurationError("Claude API key is required")
        
        if not any([self.mcp_server_path, self.mcp_server_url, self.mcp_server_command]):
            raise ConfigurationError(
                "At least one MCP server connection method must be specified: "
                "server_path, server_url, or server_command"
            )
        
        if self.connection_timeout <= 0:
            raise ConfigurationError("Connection timeout must be positive")
        
        if self.request_timeout <= 0:
            raise ConfigurationError("Request timeout must be positive")
        
        if self.max_retries < 0:
            raise ConfigurationError("Max retries must be non-negative")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "claude_api_key": "***" if self.claude_api_key else None,  # Hide API key
            "claude_model": self.claude_model,
            "claude_max_tokens": self.claude_max_tokens,
            "mcp_server_path": self.mcp_server_path,
            "mcp_server_url": self.mcp_server_url,
            "mcp_server_command": self.mcp_server_command,
            "mcp_server_args": self.mcp_server_args,
            "mcp_server_env": self.mcp_server_env,
            "connection_timeout": self.connection_timeout,
            "request_timeout": self.request_timeout,
            "max_retries": self.max_retries,
            "log_level": self.log_level,
        }


def load_config(config_path: Optional[str] = None) -> ClientConfig:
    """Load configuration from file or environment variables."""
    if config_path:
        return ClientConfig.from_file(config_path)
    
    # Try to find config file in common locations
    config_files = [
        "mcp_claude_config.json",
        "config.json",
        Path.home() / ".mcp_claude_client" / "config.json",
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            return ClientConfig.from_file(str(config_file))
    
    # Fall back to environment variables
    return ClientConfig.from_env()
