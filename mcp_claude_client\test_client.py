#!/usr/bin/env python3
"""
Test script for the MCP Claude Client.

This script tests the integration between the MCP client and Claude API.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import the client
sys.path.insert(0, str(Path(__file__).parent.parent))

from mcp_claude_client import MCPClaudeClient, ClientConfig
from mcp_claude_client.exceptions import MCPClaudeError


async def test_basic_connection():
    """Test basic connection to MCP server."""
    print("🔌 Testing MCP server connection...")
    
    try:
        # Use the simple MCP server from the parent directory
        config = ClientConfig(
            claude_api_key=os.getenv("CLAUDE_API_KEY", "test-key"),
            mcp_server_path="../simple_mcp_server.py",
            log_level="DEBUG"
        )
        
        client = MCPClaudeClient(config)
        
        async with client:
            print("✅ Successfully connected to MCP server")
            
            # Test tool discovery
            tools = client.get_available_tools()
            print(f"📋 Discovered {len(tools)} tools:")
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False


async def test_tool_execution():
    """Test MCP tool execution without Claude API."""
    print("\n🔧 Testing MCP tool execution...")
    
    try:
        config = ClientConfig(
            claude_api_key=os.getenv("CLAUDE_API_KEY", "test-key"),
            mcp_server_path="../simple_mcp_server.py",
            log_level="INFO"
        )
        
        client = MCPClaudeClient(config)
        
        async with client:
            # Test direct tool execution
            result = await client.mcp_client.call_tool("greet", {"name": "Test User"})
            print(f"✅ Tool execution successful: {result}")
            
            # Test calculation tool
            result = await client.mcp_client.call_tool("calculate_sum", {"a": 10, "b": 20})
            print(f"✅ Calculation tool successful: {result}")
            
            return True
            
    except Exception as e:
        print(f"❌ Tool execution test failed: {e}")
        return False


async def test_claude_integration():
    """Test Claude API integration (requires valid API key)."""
    print("\n🤖 Testing Claude API integration...")
    
    claude_api_key = os.getenv("CLAUDE_API_KEY")
    if not claude_api_key:
        print("⚠️  Skipping Claude API test - no API key provided")
        print("   Set CLAUDE_API_KEY environment variable to test Claude integration")
        return True
    
    try:
        config = ClientConfig(
            claude_api_key=claude_api_key,
            mcp_server_path="../simple_mcp_server.py",
            log_level="INFO"
        )
        
        client = MCPClaudeClient(config)
        
        async with client:
            # Test simple message without tools
            response = await client.send_message("Hello! Can you introduce yourself?")
            print(f"✅ Claude response: {response[:100]}...")
            
            # Test message that should trigger tool usage
            response = await client.send_message("What time is it?")
            print(f"✅ Claude with tools: {response[:100]}...")
            
            return True
            
    except Exception as e:
        print(f"❌ Claude integration test failed: {e}")
        return False


async def test_configuration():
    """Test configuration loading."""
    print("\n⚙️  Testing configuration...")
    
    try:
        # Test environment variable configuration
        config = ClientConfig.from_env()
        print("✅ Environment configuration loaded")
        
        # Test configuration validation
        try:
            config.validate()
            print("✅ Configuration validation passed")
        except Exception as e:
            print(f"⚠️  Configuration validation failed (expected): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def run_all_tests():
    """Run all tests."""
    print("🚀 MCP Claude Client Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("MCP Connection", test_basic_connection),
        ("Tool Execution", test_tool_execution),
        ("Claude Integration", test_claude_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == len(results)


if __name__ == "__main__":
    print("Starting MCP Claude Client tests...")
    print("Make sure the simple MCP server is available at ../simple_mcp_server.py")
    print("Set CLAUDE_API_KEY environment variable to test Claude integration")
    print()
    
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
