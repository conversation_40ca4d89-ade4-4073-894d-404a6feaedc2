#!/usr/bin/env python3
"""
Setup script for the MCP Claude Client.

This script helps users set up the client by:
1. Installing dependencies
2. Creating configuration files
3. Testing the setup
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def create_config_file():
    """Create a configuration file."""
    print("\n⚙️  Creating configuration file...")
    
    config_path = Path("mcp_claude_config.json")
    
    if config_path.exists():
        print(f"⚠️  Configuration file already exists: {config_path}")
        return True
    
    # Get Claude API key from user
    claude_api_key = input("Enter your Claude API key (or press Enter to skip): ").strip()
    
    if not claude_api_key:
        print("⚠️  No API key provided. You'll need to set it later.")
        claude_api_key = "your-claude-api-key-here"
    
    # Create configuration
    config = {
        "claude_api_key": claude_api_key,
        "claude_model": "claude-3-5-sonnet-20241022",
        "claude_max_tokens": 4096,
        "mcp_server_path": "../simple_mcp_server.py",
        "connection_timeout": 30,
        "request_timeout": 60,
        "max_retries": 3,
        "log_level": "INFO"
    }
    
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Configuration file created: {config_path}")
        
        if claude_api_key == "your-claude-api-key-here":
            print("⚠️  Remember to update the claude_api_key in the config file!")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create configuration file: {e}")
        return False


def create_env_file():
    """Create a .env file."""
    print("\n🔧 Creating .env file...")
    
    env_path = Path(".env")
    
    if env_path.exists():
        print(f"⚠️  .env file already exists: {env_path}")
        return True
    
    # Get Claude API key from user
    claude_api_key = input("Enter your Claude API key for .env file (or press Enter to skip): ").strip()
    
    if not claude_api_key:
        claude_api_key = "your-claude-api-key-here"
    
    env_content = f"""# Claude API settings
CLAUDE_API_KEY={claude_api_key}
CLAUDE_MODEL=claude-3-5-sonnet-20241022
CLAUDE_MAX_TOKENS=4096

# MCP Server settings
MCP_SERVER_PATH=../simple_mcp_server.py

# Client settings
MCP_CONNECTION_TIMEOUT=30
MCP_REQUEST_TIMEOUT=60
MCP_MAX_RETRIES=3
LOG_LEVEL=INFO
"""
    
    try:
        with open(env_path, 'w') as f:
            f.write(env_content)
        
        print(f"✅ .env file created: {env_path}")
        
        if claude_api_key == "your-claude-api-key-here":
            print("⚠️  Remember to update the CLAUDE_API_KEY in the .env file!")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False


def test_setup():
    """Test the setup."""
    print("\n🧪 Testing setup...")
    
    try:
        # Try to import the client
        from mcp_claude_client import MCPClaudeClient, ClientConfig
        print("✅ Client import successful")
        
        # Try to create a configuration
        config = ClientConfig.from_env()
        print("✅ Configuration loading successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Setup test failed: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 MCP Claude Client Setup")
    print("=" * 40)
    
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    steps = [
        ("Install Dependencies", install_dependencies),
        ("Create Config File", create_config_file),
        ("Create .env File", create_env_file),
        ("Test Setup", test_setup),
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}")
        print("-" * 30)
        
        if step_func():
            success_count += 1
        else:
            print(f"⚠️  {step_name} failed, but continuing...")
    
    print(f"\n🎯 Setup Results: {success_count}/{len(steps)} steps completed")
    
    if success_count == len(steps):
        print("🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Update your Claude API key in the config file or .env file")
        print("2. Make sure the simple MCP server is available at ../simple_mcp_server.py")
        print("3. Run the example: python example.py")
        print("4. Or use the CLI: python -m mcp_claude_client.cli chat")
    else:
        print("⚠️  Setup completed with some issues. Check the output above.")
    
    return success_count == len(steps)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
