[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "mcp-claude-client"
version = "0.1.0"
description = "A Python client for integrating MCP servers with Claude API"
readme = "README.md"
requires-python = ">=3.10"
authors = [
    {name = "MCP Developer", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "fastmcp>=2.0.0",
    "anthropic>=0.18.0",
    "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/example/mcp-claude-client"
Documentation = "https://github.com/example/mcp-claude-client#readme"
Repository = "https://github.com/example/mcp-claude-client"
Issues = "https://github.com/example/mcp-claude-client/issues"

[project.scripts]
mcp-claude = "mcp_claude_client.cli:main_entry"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
