"""
Exception classes for the MCP Claude Client.
"""

class MCPClaudeError(Exception):
    """Base exception for all MCP Claude Client errors."""
    pass


class MCPConnectionError(MCPClaudeError):
    """Exception raised when there's an error connecting to the MCP server."""
    pass


class Claude<PERSON>IError(MCPClaudeError):
    """Exception raised when there's an error with the Claude API."""
    pass


class ConfigurationError(MCPClaudeError):
    """Exception raised when there's an error with the client configuration."""
    pass


class ToolExecutionError(MCPClaudeError):
    """Exception raised when there's an error executing an MCP tool."""
    pass
