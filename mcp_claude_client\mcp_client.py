"""
Core MCP client functionality for connecting to and interacting with MCP servers.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Union
from fastmcp import Client
from mcp.types import Tool, Resource, Prompt

from .config import ClientConfig
from .exceptions import MCPConnectionError, ToolExecutionError


logger = logging.getLogger(__name__)


class MCPClient:
    """Client for connecting to and interacting with MCP servers."""
    
    def __init__(self, config: ClientConfig):
        """Initialize the MCP client with configuration."""
        self.config = config
        self._client: Optional[Client] = None
        self._tools: List[Tool] = []
        self._resources: List[Resource] = []
        self._prompts: List[Prompt] = []
        self._connected = False
    
    async def connect(self) -> None:
        """Connect to the MCP server."""
        try:
            # Determine connection method based on configuration
            if self.config.mcp_server_url:
                # HTTP/WebSocket connection
                self._client = Client(self.config.mcp_server_url)
                logger.info(f"Connecting to MCP server at {self.config.mcp_server_url}")
            
            elif self.config.mcp_server_path:
                # File-based connection
                self._client = Client(self.config.mcp_server_path)
                logger.info(f"Connecting to MCP server file: {self.config.mcp_server_path}")
            
            elif self.config.mcp_server_command:
                # Command-based connection
                server_config = {
                    "command": self.config.mcp_server_command,
                    "args": self.config.mcp_server_args,
                    "env": self.config.mcp_server_env,
                }
                self._client = Client(server_config)
                logger.info(f"Connecting to MCP server command: {self.config.mcp_server_command}")
            
            else:
                raise MCPConnectionError("No MCP server connection method specified")
            
            # Establish connection
            await self._client.__aenter__()
            self._connected = True
            
            # Discover available tools, resources, and prompts
            await self._discover_capabilities()
            
            logger.info("Successfully connected to MCP server")
            
        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {e}")
            raise MCPConnectionError(f"Failed to connect to MCP server: {e}")
    
    async def disconnect(self) -> None:
        """Disconnect from the MCP server."""
        if self._client and self._connected:
            try:
                await self._client.__aexit__(None, None, None)
                self._connected = False
                logger.info("Disconnected from MCP server")
            except Exception as e:
                logger.error(f"Error disconnecting from MCP server: {e}")
        
        self._client = None
        self._tools = []
        self._resources = []
        self._prompts = []
    
    async def _discover_capabilities(self) -> None:
        """Discover available tools, resources, and prompts from the server."""
        if not self._client or not self._connected:
            raise MCPConnectionError("Not connected to MCP server")
        
        try:
            # Discover tools
            self._tools = await self._client.list_tools()
            logger.info(f"Discovered {len(self._tools)} tools")
            
            # Discover resources
            try:
                self._resources = await self._client.list_resources()
                logger.info(f"Discovered {len(self._resources)} resources")
            except Exception as e:
                logger.warning(f"Could not list resources: {e}")
                self._resources = []
            
            # Discover prompts
            try:
                self._prompts = await self._client.list_prompts()
                logger.info(f"Discovered {len(self._prompts)} prompts")
            except Exception as e:
                logger.warning(f"Could not list prompts: {e}")
                self._prompts = []
                
        except Exception as e:
            logger.error(f"Failed to discover server capabilities: {e}")
            raise MCPConnectionError(f"Failed to discover server capabilities: {e}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Call a tool on the MCP server."""
        if not self._client or not self._connected:
            raise MCPConnectionError("Not connected to MCP server")
        
        try:
            logger.info(f"Calling tool '{tool_name}' with arguments: {arguments}")
            result = await self._client.call_tool(tool_name, arguments)
            logger.info(f"Tool '{tool_name}' executed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Failed to call tool '{tool_name}': {e}")
            raise ToolExecutionError(f"Failed to call tool '{tool_name}': {e}")
    
    async def read_resource(self, resource_uri: str) -> Any:
        """Read a resource from the MCP server."""
        if not self._client or not self._connected:
            raise MCPConnectionError("Not connected to MCP server")
        
        try:
            logger.info(f"Reading resource: {resource_uri}")
            result = await self._client.read_resource(resource_uri)
            logger.info(f"Resource '{resource_uri}' read successfully")
            return result
            
        except Exception as e:
            logger.error(f"Failed to read resource '{resource_uri}': {e}")
            raise ToolExecutionError(f"Failed to read resource '{resource_uri}': {e}")
    
    async def get_prompt(self, prompt_name: str, arguments: Optional[Dict[str, Any]] = None) -> Any:
        """Get a prompt from the MCP server."""
        if not self._client or not self._connected:
            raise MCPConnectionError("Not connected to MCP server")
        
        try:
            logger.info(f"Getting prompt '{prompt_name}' with arguments: {arguments}")
            result = await self._client.get_prompt(prompt_name, arguments or {})
            logger.info(f"Prompt '{prompt_name}' retrieved successfully")
            return result
            
        except Exception as e:
            logger.error(f"Failed to get prompt '{prompt_name}': {e}")
            raise ToolExecutionError(f"Failed to get prompt '{prompt_name}': {e}")
    
    @property
    def tools(self) -> List[Tool]:
        """Get the list of available tools."""
        return self._tools.copy()
    
    @property
    def resources(self) -> List[Resource]:
        """Get the list of available resources."""
        return self._resources.copy()
    
    @property
    def prompts(self) -> List[Prompt]:
        """Get the list of available prompts."""
        return self._prompts.copy()
    
    @property
    def is_connected(self) -> bool:
        """Check if connected to the MCP server."""
        return self._connected
    
    def get_tool_by_name(self, name: str) -> Optional[Tool]:
        """Get a tool by its name."""
        for tool in self._tools:
            if tool.name == name:
                return tool
        return None
    
    def get_tools_summary(self) -> Dict[str, str]:
        """Get a summary of available tools."""
        return {
            tool.name: tool.description or "No description available"
            for tool in self._tools
        }
