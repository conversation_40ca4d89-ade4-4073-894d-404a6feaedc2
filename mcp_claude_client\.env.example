# Claude API settings
CLAUDE_API_KEY=your-claude-api-key-here
CLAUDE_MODEL=claude-3-5-sonnet-20241022
CLAUDE_MAX_TOKENS=4096

# MCP Server settings
# Choose one of the following connection methods:

# 1. File path to MCP server
MCP_SERVER_PATH=../simple_mcp_server.py

# 2. HTTP URL for MCP server
# MCP_SERVER_URL=http://localhost:8000

# 3. Command to start MCP server
# MCP_SERVER_COMMAND=python
# MCP_SERVER_ARGS=../simple_mcp_server.py

# Client settings
MCP_CONNECTION_TIMEOUT=30
MCP_REQUEST_TIMEOUT=60
MCP_MAX_RETRIES=3
LOG_LEVEL=INFO
