"""
Command-line interface for the MCP Claude Client.
"""

import asyncio
import argparse
import sys
from pathlib import Path
from typing import Op<PERSON>

from .client import MCPClaudeClient
from .config import load_config
from .logging_utils import setup_logging


class MCPClaudeCLI:
    """Command-line interface for the MCP Claude Client."""
    
    def __init__(self):
        self.client: Optional[MCPClaudeClient] = None
    
    def create_parser(self) -> argparse.ArgumentParser:
        """Create the argument parser."""
        parser = argparse.ArgumentParser(
            description="MCP Claude Client - Chat with <PERSON> using MCP tools",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  # Interactive chat mode
  python -m mcp_claude_client.cli chat
  
  # Send a single message
  python -m mcp_claude_client.cli message "What time is it?"
  
  # List available tools
  python -m mcp_claude_client.cli tools
  
  # Use custom config file
  python -m mcp_claude_client.cli --config config.json chat
            """
        )
        
        parser.add_argument(
            "--config",
            type=str,
            help="Path to configuration file"
        )
        
        parser.add_argument(
            "--log-level",
            choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
            default="INFO",
            help="Logging level"
        )
        
        parser.add_argument(
            "--log-file",
            type=str,
            help="Path to log file"
        )
        
        subparsers = parser.add_subparsers(dest="command", help="Available commands")
        
        # Chat command
        chat_parser = subparsers.add_parser("chat", help="Start interactive chat")
        chat_parser.add_argument(
            "--no-history",
            action="store_true",
            help="Don't include conversation history"
        )
        
        # Message command
        message_parser = subparsers.add_parser("message", help="Send a single message")
        message_parser.add_argument("text", help="Message text to send")
        message_parser.add_argument(
            "--no-history",
            action="store_true",
            help="Don't include conversation history"
        )
        
        # Tools command
        subparsers.add_parser("tools", help="List available tools")
        
        # Config command
        config_parser = subparsers.add_parser("config", help="Show current configuration")
        config_parser.add_argument(
            "--show-keys",
            action="store_true",
            help="Show API keys (masked by default)"
        )
        
        return parser
    
    async def initialize_client(self, config_path: Optional[str] = None) -> None:
        """Initialize the MCP Claude client."""
        try:
            config = load_config(config_path)
            self.client = MCPClaudeClient(config)
            await self.client.connect()
            print("✅ Connected to MCP server and Claude API")
        except Exception as e:
            print(f"❌ Failed to initialize client: {e}")
            sys.exit(1)
    
    async def cleanup(self) -> None:
        """Clean up resources."""
        if self.client:
            await self.client.disconnect()
    
    async def cmd_chat(self, args) -> None:
        """Interactive chat mode."""
        print("🤖 MCP Claude Client - Interactive Chat")
        print("Type 'quit', 'exit', or press Ctrl+C to exit")
        print("Type 'clear' to clear conversation history")
        print("Type 'tools' to list available tools")
        print("-" * 50)
        
        try:
            while True:
                try:
                    user_input = input("\n👤 You: ").strip()
                    
                    if not user_input:
                        continue
                    
                    if user_input.lower() in ["quit", "exit"]:
                        break
                    
                    if user_input.lower() == "clear":
                        self.client.clear_history()
                        print("🧹 Conversation history cleared")
                        continue
                    
                    if user_input.lower() == "tools":
                        await self.cmd_tools(args)
                        continue
                    
                    print("🤖 Claude: ", end="", flush=True)
                    
                    response = await self.client.send_message(
                        user_input,
                        include_history=not args.no_history
                    )
                    
                    print(response)
                    
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"\n❌ Error: {e}")
        
        except KeyboardInterrupt:
            pass
        
        print("\n👋 Goodbye!")
    
    async def cmd_message(self, args) -> None:
        """Send a single message."""
        try:
            response = await self.client.send_message(
                args.text,
                include_history=not args.no_history
            )
            print(response)
        except Exception as e:
            print(f"❌ Error: {e}")
            sys.exit(1)
    
    async def cmd_tools(self, args) -> None:
        """List available tools."""
        tools = self.client.get_available_tools()
        
        if not tools:
            print("No tools available")
            return
        
        print(f"📋 Available Tools ({len(tools)}):")
        print("-" * 50)
        
        for tool in tools:
            print(f"🔧 {tool['name']}")
            print(f"   {tool['description']}")
            print()
    
    async def cmd_config(self, args) -> None:
        """Show current configuration."""
        config_dict = self.client.config.to_dict()
        
        print("⚙️  Current Configuration:")
        print("-" * 50)
        
        for key, value in config_dict.items():
            if not args.show_keys and "api_key" in key.lower():
                value = "***" if value else None
            print(f"{key}: {value}")
    
    async def run(self, argv: Optional[list] = None) -> None:
        """Run the CLI."""
        parser = self.create_parser()
        args = parser.parse_args(argv)
        
        # Setup logging
        setup_logging(
            level=args.log_level,
            log_file=args.log_file
        )
        
        if not args.command:
            parser.print_help()
            return
        
        try:
            # Initialize client
            await self.initialize_client(args.config)
            
            # Run command
            if args.command == "chat":
                await self.cmd_chat(args)
            elif args.command == "message":
                await self.cmd_message(args)
            elif args.command == "tools":
                await self.cmd_tools(args)
            elif args.command == "config":
                await self.cmd_config(args)
            
        finally:
            await self.cleanup()


async def main(argv: Optional[list] = None) -> None:
    """Main entry point for the CLI."""
    cli = MCPClaudeCLI()
    await cli.run(argv)


def main_entry():
    """Entry point for the CLI script."""
    asyncio.run(main(sys.argv[1:]))


if __name__ == "__main__":
    asyncio.run(main())
