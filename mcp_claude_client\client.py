"""
Main MCP Claude Client that integrates MCP server communication with Claude API.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from anthropic.types import Message, MessageParam

from .config import ClientConfig
from .mcp_client import MC<PERSON><PERSON>
from .claude_client import <PERSON><PERSON><PERSON>
from .exceptions import MCPClaudeError, MCPConnectionError, ClaudeAPIError, ToolExecutionError


logger = logging.getLogger(__name__)


class MCPClaudeClient:
    """
    Main client that integrates MCP server communication with Claude API.
    
    This client can:
    - Connect to MCP servers and discover available tools
    - Send messages to <PERSON> with access to MCP tools
    - Execute MCP tools based on <PERSON>'s responses
    - Handle conversation flow with tool execution
    """
    
    def __init__(self, config: Optional[ClientConfig] = None):
        """Initialize the MCP Claude client."""
        self.config = config or ClientConfig.from_env()
        self.config.validate()
        
        self.mcp_client = MCPClient(self.config)
        self.claude_client = ClaudeClient(self.config)
        
        self._conversation_history: List[MessageParam] = []
        
        # Setup logging
        logging.basicConfig(
            level=getattr(logging, self.config.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        logger.info("MCP Claude Client initialized")
    
    async def connect(self) -> None:
        """Connect to the MCP server."""
        await self.mcp_client.connect()
        logger.info("MCP Claude Client connected and ready")
    
    async def disconnect(self) -> None:
        """Disconnect from the MCP server."""
        await self.mcp_client.disconnect()
        logger.info("MCP Claude Client disconnected")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
    
    def get_system_prompt(self) -> str:
        """Generate system prompt with available tools information."""
        base_prompt = """You are a helpful AI assistant with access to various tools through the Model Context Protocol (MCP).

You can use the available tools to help users with their requests. When you need to use a tool, I will execute it for you and provide the results.

Always be helpful, accurate, and explain what you're doing when using tools."""
        
        if self.mcp_client.is_connected and self.mcp_client.tools:
            tools_info = self.claude_client.format_tools_for_system_prompt(self.mcp_client.tools)
            return f"{base_prompt}\n\n{tools_info}"
        
        return base_prompt
    
    async def send_message(
        self,
        message: str,
        include_history: bool = True,
        max_tool_iterations: int = 5
    ) -> str:
        """
        Send a message to Claude and handle any tool executions.
        
        Args:
            message: The user message to send
            include_history: Whether to include conversation history
            max_tool_iterations: Maximum number of tool execution iterations
            
        Returns:
            Claude's final response as a string
        """
        if not self.mcp_client.is_connected:
            raise MCPConnectionError("Not connected to MCP server")
        
        # Create user message
        user_message = self.claude_client.create_user_message(message)
        
        # Prepare messages for Claude
        messages = []
        if include_history:
            messages.extend(self._conversation_history)
        messages.append(user_message)
        
        # Add to conversation history
        self._conversation_history.append(user_message)
        
        # Get available tools in Claude format
        claude_tools = self.claude_client._convert_mcp_tools_to_claude_format(self.mcp_client.tools)
        
        # Send message to Claude and handle tool executions
        final_response = await self._handle_conversation_with_tools(
            messages, claude_tools, max_tool_iterations
        )
        
        return final_response
    
    async def _handle_conversation_with_tools(
        self,
        messages: List[MessageParam],
        tools: List[Dict[str, Any]],
        max_iterations: int
    ) -> str:
        """Handle conversation with Claude including tool executions."""
        current_messages = messages.copy()
        
        for iteration in range(max_iterations):
            logger.info(f"Conversation iteration {iteration + 1}/{max_iterations}")
            
            # Send message to Claude
            response = await self.claude_client.send_message(
                messages=current_messages,
                tools=tools if tools else None,
                system_prompt=self.get_system_prompt()
            )
            
            # Add Claude's response to conversation history
            assistant_message = {
                "role": "assistant",
                "content": response.content
            }
            current_messages.append(assistant_message)
            self._conversation_history.append(assistant_message)
            
            # Check if Claude wants to use any tools
            tool_calls = self.claude_client.extract_tool_calls(response)
            
            if not tool_calls:
                # No tools to execute, return the text response
                text_response = self.claude_client.extract_text_content(response)
                logger.info("Conversation completed without tool usage")
                return text_response
            
            # Execute tools and add results
            logger.info(f"Executing {len(tool_calls)} tool calls")
            
            for tool_call in tool_calls:
                try:
                    # Execute the MCP tool
                    tool_result = await self.mcp_client.call_tool(
                        tool_call["name"],
                        tool_call["input"]
                    )
                    
                    # Create tool result message
                    tool_result_message = self.claude_client.create_tool_result_message(
                        tool_call["id"],
                        tool_result
                    )
                    
                    current_messages.append(tool_result_message)
                    self._conversation_history.append(tool_result_message)
                    
                    logger.info(f"Tool '{tool_call['name']}' executed successfully")
                    
                except Exception as e:
                    logger.error(f"Tool execution failed: {e}")
                    
                    # Create error result message
                    error_message = self.claude_client.create_tool_result_message(
                        tool_call["id"],
                        f"Error executing tool: {str(e)}"
                    )
                    
                    current_messages.append(error_message)
                    self._conversation_history.append(error_message)
        
        # If we've reached max iterations, send one final message to Claude
        logger.warning(f"Reached maximum tool iterations ({max_iterations})")
        final_response = await self.claude_client.send_message(
            messages=current_messages,
            system_prompt=self.get_system_prompt()
        )
        
        # Add final response to history
        final_assistant_message = {
            "role": "assistant",
            "content": final_response.content
        }
        self._conversation_history.append(final_assistant_message)
        
        return self.claude_client.extract_text_content(final_response)
    
    def clear_history(self) -> None:
        """Clear the conversation history."""
        self._conversation_history.clear()
        logger.info("Conversation history cleared")
    
    def get_available_tools(self) -> List[Dict[str, str]]:
        """Get a list of available tools with their descriptions."""
        if not self.mcp_client.is_connected:
            return []
        
        return [
            {
                "name": tool.name,
                "description": tool.description or "No description available"
            }
            for tool in self.mcp_client.tools
        ]
    
    def get_conversation_history(self) -> List[MessageParam]:
        """Get the current conversation history."""
        return self._conversation_history.copy()
    
    @property
    def is_connected(self) -> bool:
        """Check if connected to the MCP server."""
        return self.mcp_client.is_connected
