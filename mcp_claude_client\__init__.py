"""
MCP Claude Client - A Python client for integrating MCP servers with Claude API.

This package provides a simple interface for connecting to MCP servers and
using <PERSON>'s language model capabilities with the available tools.
"""

from .client import MCPClaudeClient
from .config import ClientConfig
from .exceptions import MCPClaudeError, MCPConnectionError, ClaudeAPIError

__version__ = "0.1.0"
__author__ = "MCP Developer"

__all__ = [
    "MCPClaudeClient",
    "ClientConfig", 
    "MCPClaudeError",
    "MCPConnectionError",
    "ClaudeAPIError",
]
